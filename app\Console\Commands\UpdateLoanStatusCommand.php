<?php

namespace App\Console\Commands;

use App\Jobs\UpdateLoanStatusJob;
use Illuminate\Console\Command;

class UpdateLoanStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'loan:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update loan statuses based on installment due dates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting loan status update...');

        try {
            $job = new UpdateLoanStatusJob;
            $job->handle();

            $this->info('Loan status update completed successfully!');
        } catch (\Exception $e) {
            $this->error('Loan status update failed: '.$e->getMessage());

            return 1;
        }

        return 0;
    }
}
