<?php

use App\Jobs\TestingJob;
use App\Jobs\UpdateLoanStatusJob;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

if (TestingJob::isEnabled()) {
    Schedule::job(new TestingJob)->cron(TestingJob::getFrequency());
}

if (UpdateLoanStatusJob::isEnabled()) {
    Schedule::job(new UpdateLoanStatusJob)->cron(UpdateLoanStatusJob::getFrequency());
}
