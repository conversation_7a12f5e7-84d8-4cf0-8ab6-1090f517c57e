<?php

namespace App\Jobs;

use App\Enums\Loan\LoanInstallmentStatus;
use App\Enums\Loan\LoanStatus;
use App\Models\Loan;
use App\Models\LoanInstallment;
use Carbon\Carbon;
use Illuminate\Bus\Queueable as BusQueueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateLoanStatusJob implements ShouldQueue
{
    use BusQueueable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Check if the job is enabled.
     */
    public static function isEnabled(): bool
    {
        return (bool) env('UPDATE_LOAN_STATUS_JOB_ENABLED', true);
    }

    /**
     * Get the cron frequency for the job.
     */
    public static function getFrequency(): string
    {
        return env('UPDATE_LOAN_STATUS_JOB_FREQUENCY', '0 1 * * *'); // Daily at 1:00 AM
    }

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('UpdateLoanStatusJob started: '.Carbon::now());

        try {
            DB::beginTransaction();

            $this->updateOverdueInstallments();
            $this->updateLoanStatuses();
            $this->updateCompletedLoans();

            DB::commit();

            Log::info('UpdateLoanStatusJob completed successfully: '.Carbon::now());
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('UpdateLoanStatusJob failed: '.$e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Update installments that are past due date to overdue status
     */
    private function updateOverdueInstallments(): void
    {
        $today = Carbon::today();

        $overdueCount = LoanInstallment::where('status', LoanInstallmentStatus::UNPAID)
            ->whereDate('due_date', '<', $today)
            ->update(['status' => LoanInstallmentStatus::OVERDUE]);

        Log::info("Updated {$overdueCount} installments to overdue status");
    }

    /**
     * Update loan statuses based on installment status
     */
    private function updateLoanStatuses(): void
    {
        // Update loans to ON_GOING_OVERDUE if they have overdue installments
        $overdueLoans = Loan::whereIn('status', [LoanStatus::ON_GOING, LoanStatus::CUSTOMER_ACCEPTED])
            ->whereHas('installments', function ($query) {
                $query->where('status', LoanInstallmentStatus::OVERDUE);
            })
            ->where('status', '!=', LoanStatus::ON_GOING_OVERDUE)
            ->get();

        foreach ($overdueLoans as $loan) {
            $loan->update(['status' => LoanStatus::ON_GOING_OVERDUE]);
            Log::info("Updated loan {$loan->code} to ON_GOING_OVERDUE status");
        }

        // Update loans back to ON_GOING if they no longer have overdue installments
        $noLongerOverdueLoans = Loan::where('status', LoanStatus::ON_GOING_OVERDUE)
            ->whereDoesntHave('installments', function ($query) {
                $query->where('status', LoanInstallmentStatus::OVERDUE);
            })
            ->get();

        foreach ($noLongerOverdueLoans as $loan) {
            $loan->update(['status' => LoanStatus::ON_GOING]);
            Log::info("Updated loan {$loan->code} back to ON_GOING status");
        }
    }

    /**
     * Update loans to completed status if all installments are paid
     */
    private function updateCompletedLoans(): void
    {
        $completedLoans = Loan::whereIn('status', [LoanStatus::ON_GOING, LoanStatus::ON_GOING_OVERDUE])
            ->whereHas('installments')
            ->whereDoesntHave('installments', function ($query) {
                $query->whereIn('status', [
                    LoanInstallmentStatus::UNPAID,
                    LoanInstallmentStatus::OVERDUE,
                    LoanInstallmentStatus::PARTIALLY_PAID,
                ]);
            })
            ->get();

        foreach ($completedLoans as $loan) {
            $loan->update(['status' => LoanStatus::COMPLETED]);
            Log::info("Updated loan {$loan->code} to COMPLETED status");
        }
    }
}
